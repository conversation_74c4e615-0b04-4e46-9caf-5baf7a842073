"use client";

import DashboardLayout from "../../../../components/layouts/DashboardLayout";
import CountdownWidget from "../../../../components/dashboard/CountdownWidget";
import React, {Suspense, useContext, useEffect, useRef, useState} from "react";
import strapi from "@/app/api/strapi";
import Cookies from "universal-cookie";
import { FaPlay } from 'react-icons/fa';
import TextField from "@/components/TextField";
import {useParams, useRouter, useSearchParams} from "next/navigation";
import Hls from "hls.js";
import Button from "@/components/Button";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import ArrowLeftIcon from "@/components/icons/ArrowLeftIcon";
import LineDivider from "@/components/icons/LineDivider";
import FileTextIcon from "@/components/icons/FileTextIcon";
import ArrowDownIcon from "@/components/icons/ArrowDownIcon";
import ChevronDownIcon from "@/components/icons/ChevronDownIcon";
import ChevronUpIcon from "@/components/icons/ChevronUpIcon";
import {useNotification} from "@/context/NotificationContext";
import {useDashboardLayout} from "@/context/DashboardLayoutContext";
import {useScreenSize} from "@/hooks/useScreenSize";
import clsx from "clsx";
import {UserContext} from "@/context/UserProvider";


export default function viewDetailVideo() {
    return (
        <Suspense fallback={<div>Đang tải...</div>}>
            <ViewDetailContent />
        </Suspense>
    );
    function ViewDetailContent() {
        const router = useRouter();
        const searchParams = useSearchParams();
        const videoId = searchParams.get('videoId');
        const collectionId = searchParams.get('collectionId');
        const videoRef = useRef(null);
        const [videoUrl, setVideoUrl] = useState("");
        const [src, setSrc] = useState("");
        const [formData, setFormData] = useState({});
        const [videos, setListVideos] = useState([]);
        const screenSize = useScreenSize();
        const [isExpandChapter, setIsExpandChapter] = useState(false);
        const [isExpandExercise, setIsExpandExercise] = useState(false);
        const {showNotification} = useNotification();
        const [chapters, setListChapters] = useState([
            { time: '00:00:00', label: 'Thành phần nguyên tử' },
            { time: '00:36:12', label: 'Cấu tạo nguyên tử' },
            { time: '00:46:39', label: 'Khối lượng, kích thước nguyên tử' },
            { time: '00:50:42', label: 'Nguyên tử khối và nguyên tử khối trung bình...' },
            { time: '01:20:17', label: 'Khối lượng, kích thước nguyên tử' },
            { time: '01:20:17', label: 'Khối lượng, kích thước nguyên tử' },
            { time: '01:20:17', label: 'Khối lượng, kích thước nguyên tử' },
            { time: '01:20:17', label: 'Khối lượng, kích thước nguyên tử' },
        ]);
        const [exercise, setListExercise] = useState([
            { label: 'Tổng hợp', link: '#' },
            { label: 'Thành phần nguyên tử', link: '#' },
            { label: 'Cấu tạo nguyên tử', link: '#' },
            { label: 'Khối lượng, kích thước nguyên tử', link: '#' },
            { label: 'Khối lượng, kích thước nguyên tử', link: '#' },
            { label: 'Khối lượng, kích thước nguyên tử', link: '#' },
            { label: 'Khối lượng, kích thước nguyên tử', link: '#' },
            { label: 'Khối lượng, kích thước nguyên tử', link: '#' },
        ]);

        const {setTitle, keySearch, setKeySearch, setIsSearch, setIsDetail, setIsTurnLive} = useDashboardLayout();

        useEffect(() => {
            setTitle("");
            setIsSearch(false);
            setIsDetail(true);
            setIsTurnLive(false);
            setKeySearch(keySearch);
            return () => {
                setIsSearch(false);
                setIsTurnLive(false);
                setIsDetail(false);
            }
        }, []);


        useEffect(() => {
            const getVideo = async () => {
                const cookies = new Cookies();
                const user_data = cookies.get('user_data');
                const data = { videoId: videoId, user_id: user_data.id };
                try {
                    const res = await strapi.bunny.getSrcVideoPlay(data);
                    if (res.data == null) {
                        showNotification({
                            type: "error",
                            title: "Lỗi",
                            message:  "Không thể truy cập video này",
                            duration: 5000,
                        });
                    }else {
                        setSrc(res.data.iframeUrl);
                        setFormData({title: res.data.title, description:  res.data.description, createdAt: res.data.createdAt, documentFile: res.data.documentFile});
                    }

                } catch (error) {
                    console.error("Lỗi khi lấy video:", error);
                }
            };
            const getListVideo = async () => {
                const  res = await strapi.quanLy.getVideoUpload(collectionId);
                if(res) {
                    if (res.data && res.data.length > 0) {
                        // setListVideos(res.data);
                        let a = res.data.filter(video => video.video_id !== videoId);
                        setListVideos(a);
                    }else {
                        setListVideos([]);
                    }
                }
            }
            if (collectionId) {
                getListVideo();
            }
            if (videoId) {
                getVideo();
            }
        }, [videoId]);
        useEffect(() => {
            console.log(videos)
        }, [videos]);
        const clickBack = () => {
            router.push("/quan-ly/xem-video");
        }
        const onClickVideo = (video) => {
            router.push( '/quan-ly/xem-video/chi-tiet?videoId=' + video.video_id + '&collectionId=' + video.collection_id);
        }
        const handleDownload = async () => {
            const documentFile = formData.documentFile;
            if ( !documentFile || !documentFile?.url) {
                showNotification({
                    type: "error",
                    title: "Không có file",
                    message:  "Không có file để download",
                    duration: 5000,
                });
                return;
            }
            // Tạo URL đầy đủ

            const fullUrl =  process.env.NEXT_PUBLIC_STRAPI_URL + documentFile.url;
            try {
                const response = await fetch(fullUrl);
                const blob = await response.blob();
                const link = document.createElement("a");
                link.href = URL.createObjectURL(blob);
                link.download = documentFile.name || 'tai-lieu.pdf';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(link.href);
            } catch (error) {
                console.error(error);
                showNotification({
                    type: "error",
                    title: "Lỗi tải file",
                    message: "Có lỗi xảy ra khi tải file",
                    duration: 5000,
                });
            }
        }
        if (src) {
            return (
                // <DashboardLayout isDetail={true}>
                //
                // </DashboardLayout>

            <div className="flex flex-col min-h-[calc(100vh-160px)] bg-gray-50">
                {/* Main content + Sidebar */}
                <div className="flex flex-col">
                    {/*Video + mục lục + bài tập liên quan*/}
                    <div className="flex flex-col md:flex-row">
                        {/*Video*/}
                        <div className="flex flex-col md:flex-1">
                            {/* Video iframe */}
                            <div className="relative w-full overflow-hidden shadow-md md:rounded-md xs:rounded-none aspect-video">
                                <iframe
                                    src={src}
                                    loading="lazy"
                                    className="absolute top-0 left-0 w-full h-full border-0 md:rounded-lg"
                                    allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture"
                                    allowFullScreen
                                    title="Video Player"
                                ></iframe>
                            </div>

                            {/* Video info */}
                            <div className="bg-white xs:mt-2xl md:mt-[20px] mb-4xl flex flex-row gap-[55px] relative">
                                <div className="w-full md:w-3/4">
                                    <div className="flex justify-between items-center">
                                        <p className="text-primary-900 xs:text-lg leading-lg md:text-xl font-semibold md:leading-xl">{formData.title}</p>
                                    </div>
                                    <div className="mt-lg mb-lg flex flex-row justify-between">
                                        <p className= "text-tertiary-600 text-sm font-normal leading-sm">
                                            {new Date(formData.createdAt).toLocaleDateString('vi-VN')}
                                        </p>
                                        <div className="w-full md:hidden flex justify-end">
                                            <button onClick={handleDownload} className="flex items-center rounded-md py-md px-lg bg-white border button-secondary-border shadow-skeu-var">
                                                <ArrowDownIcon></ArrowDownIcon>
                                                <p className="ml-xs text-sm font-semibold text-tertiary-fg leading-sm">Tải tài liệu</p>
                                            </button>
                                        </div>
                                    </div>

                                    <p className="text-tertiary-600 text-sm leading-sm md:text-md md:leading-md font-normal">{formData.description}</p>
                                </div>
                                <div className="w-1/4 relative hidden md:block">
                                    <button onClick={handleDownload} className="flex items-center rounded-md min-w-[124px] py-[10px] px-[14px] bg-white border button-secondary-border shadow-skeu-var absolute top-0 right-0">
                                        <ArrowDownIcon></ArrowDownIcon>
                                        <p className="ml-xs text-sm font-semibold text-tertiary-fg leading-sm">Tải tài liệu</p>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {/*Mục lục + bài tập liên quan*/}
                        <div className={clsx(
                            "w-[311px] md:ml-xl flex flex-col xs:w-full md:w-[314px]",
                            screenSize.lte960 ? 'hidden' : ''
                        )}>
                            <div className="video_live_cu bg-white flex mb-xl flex-col rounded-lg border border-secondary gap-0">
                                <div className="flex flex-row justify-between items-center py-lg px-xl">
                                    <p className="text-primary-900 text-lg font-semibold leading-lg">Bài livestream cũ</p>
                                    <p className={clsx(
                                        "text-tertiary-fg font-semibold text-sm leading-sm cursor-pointer",
                                        videos.length > 5 ? '': 'hidden'
                                    )} onClick={clickBack}>Xem thêm</p>
                                </div>
                                <LineDivider></LineDivider>
                                {
                                    videos.length === 0 ?
                                        <div className="gap-y-lg flex flex-col py-2xl px-xl cursor-pointer items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="96" height="120" viewBox="0 0 96 120" fill="none">
                                                <path opacity="0.27" d="M32.9312 16.873L1.04278 104.795C0.577715 106.075 0.623139 107.485 1.16963 108.732C1.71612 109.979 2.72172 110.968 3.97782 111.493C11.3735 114.597 27.2411 119.999 48.1561 119.999C69.071 119.999 84.9345 114.599 92.3359 111.493C93.5918 110.967 94.5971 109.978 95.1433 108.731C95.6896 107.484 95.7348 106.075 95.2696 104.795L63.3812 16.873H32.9312Z" fill="#FF97C9"/>
                                                <path d="M14.5134 27.0729C16.1534 27.0729 17.4829 25.7428 17.4829 24.1019C17.4829 22.461 16.1534 21.1309 14.5134 21.1309C12.8734 21.1309 11.5439 22.461 11.5439 24.1019C11.5439 25.7428 12.8734 27.0729 14.5134 27.0729Z" fill="#738DE0"/>
                                                <path d="M81.7988 27.0729C83.4388 27.0729 84.7683 25.7428 84.7683 24.1019C84.7683 22.461 83.4388 21.1309 81.7988 21.1309C80.1588 21.1309 78.8293 22.461 78.8293 24.1019C78.8293 25.7428 80.1588 27.0729 81.7988 27.0729Z" fill="#738DE0"/>
                                                <path d="M48.1562 31.9519C49.7962 31.9519 51.1257 30.6217 51.1257 28.9808C51.1257 27.3399 49.7962 26.0098 48.1562 26.0098C46.5162 26.0098 45.1868 27.3399 45.1868 28.9808C45.1868 30.6217 46.5162 31.9519 48.1562 31.9519Z" fill="#738DE0"/>
                                                <path d="M87.9288 18.3413C87.9288 23.894 71.8094 28.394 47.9878 28.394C24.1663 28.394 8.38965 23.8937 8.38965 18.3413C8.38965 12.789 24.1584 8.29004 47.9816 8.29004C71.8047 8.29004 87.9288 12.79 87.9288 18.3413Z" fill="#99ADF9"/>
                                                <g  opacity="0.42">
                                                    <path d="M87.9298 14.6727C87.9298 20.2238 71.8105 24.7253 47.9889 24.7253C24.1673 24.7253 8.39697 20.2238 8.39697 14.6727C8.39697 9.12168 24.1658 4.62012 47.9889 4.62012C71.812 4.62012 87.9298 9.12168 87.9298 14.6727Z" fill="white"/>
                                                </g>
                                                <path d="M48.1973 18.0477C54.5242 18.0823 60.8305 17.3232 66.9685 15.7883C65.7554 6.88696 57.471 0 47.4306 0C37.5419 0 29.3563 6.68217 27.9539 15.3913C33.8555 17.077 40.7847 18.0477 48.1973 18.0477Z" fill="#99ADF9"/>
                                                <path d="M63.5846 86.2754C63.7377 84.7991 62.3337 84.082 59.9381 83.9114C57.5426 83.7407 56.0929 84.2523 55.9398 85.7283C55.885 86.2388 56.0106 86.7523 56.2946 87.18C56.3467 87.2619 56.3744 87.3569 56.3744 87.454C56.3744 87.551 56.3467 87.646 56.2946 87.7279C56.0687 88.0936 55.8926 88.4713 56.0214 88.6164C56.2074 88.8251 56.6475 88.6425 57.1521 88.2971C57.2232 88.2488 57.3056 88.2197 57.3913 88.2124C57.477 88.2052 57.5631 88.22 57.6414 88.2556C57.9777 88.3999 58.3291 88.5061 58.6891 88.572C58.7889 88.59 58.8811 88.6371 58.9541 88.7074C59.0272 88.7777 59.0779 88.868 59.0997 88.967C59.1855 89.3967 59.3559 89.826 59.695 89.854C60.1244 89.8941 60.3472 89.5474 60.4307 89.0393C60.4461 88.9323 60.4953 88.833 60.5709 88.7557C60.6465 88.6785 60.7448 88.6273 60.8514 88.6096C61.0362 88.5807 61.2191 88.541 61.3993 88.4907C61.4852 88.4687 61.5753 88.4698 61.6607 88.4936C61.7461 88.5175 61.8237 88.5633 61.8858 88.6266C62.2951 89.026 62.6744 89.2393 62.8961 89.0562C63.0394 88.9344 62.952 88.5551 62.8132 88.1972C62.7762 88.105 62.7665 88.0042 62.7852 87.9066C62.804 87.8091 62.8505 87.7191 62.9191 87.6473C63.293 87.2807 63.5282 86.7959 63.5846 86.2754Z" fill="#FF97C9"/>
                                                <path d="M32.1181 73.4054C30.4639 77.128 30.1146 84.5047 29.8699 84.9953C29.6253 85.486 27.9199 86.3704 28.3559 87.268C28.7566 88.0927 31.5343 89.2167 32.3628 88.9861C33.6507 88.6368 34.923 85.2004 35.7059 83.0242C38.5679 84.7423 43.3535 85.3238 43.3535 85.3238C43.3535 85.3238 42.8678 88.8946 42.4947 89.6764C42.1217 90.4582 40.8147 91.4133 41.0837 92.3667C41.3526 93.3202 43.9316 93.8587 45.0763 94.006C46.221 94.1534 48.0745 87.8665 48.5026 85.6658C56.1173 86.4719 65.3091 85.8862 65.3091 85.8862C65.3091 85.8862 66.4097 89.7249 65.3333 91.4373C64.8424 92.2199 64.5306 93.8714 64.7952 93.9815C66.4337 94.6415 68.546 94.2685 69.22 93.7854C69.9784 93.2469 72.6432 80.859 71.0289 76.1829C69.4146 71.5068 66.2263 68.8264 60.615 67.4088C55.0037 65.9913 46.5346 65.9258 46.5346 65.9258L32.1181 73.4054Z" fill="white"/>
                                                <path d="M59.3587 65.9326C59.3587 65.9326 58.0708 70.8237 60.6795 73.4052C63.2882 75.9868 68.6289 75.6573 71.5282 72.2811C68.7536 67.2197 59.3587 65.9326 59.3587 65.9326Z" fill="#738DE0"/>
                                                <path d="M28.8512 79.875C28.8512 79.875 33.7816 81.6547 35.086 86.4054C32.1509 91.0473 27.6118 89.6297 27.6118 89.6297" fill="#738DE0"/>
                                                <path d="M65.2502 75.1099C66.7656 75.1099 67.8662 75.6196 68.526 76.5919C69.0282 77.3322 70.5052 79.1291 72.1552 78.1396C74.3147 76.8509 70.9946 72.3666 70.9946 72.3666L66.8629 72.0488L65.2502 75.1099Z" fill="#738DE0"/>
                                                <path d="M31.7031 74.5277C31.7031 74.5277 34.136 80.1424 40.2422 80.1374C45.5916 80.1374 48.9727 74.0065 47.3475 69.7598C42.0998 71.896 31.7031 74.5277 31.7031 74.5277Z" fill="#F0F4FF"/>
                                                <path d="M30.4292 57.827C30.4292 57.827 28.9422 54.3606 29.8769 52.9101C30.8116 51.4597 33.8354 56.5798 33.8354 56.5798L30.4292 57.827Z" fill="#FEC272"/>
                                                <path d="M46.4892 57.827C46.4892 57.827 47.973 54.3606 47.0386 52.9101C46.1042 51.4597 43.0789 56.5798 43.0789 56.5798L46.4892 57.827Z" fill="#FEC272"/>
                                                <path d="M48.4568 66.5998C47.983 72.1193 46.1329 76.8728 37.2615 76.1098C28.3902 75.3468 27.2269 70.3347 27.7007 64.8157C28.1744 59.2968 33.2058 55.2228 38.9374 55.714C44.6689 56.2052 48.9305 61.0803 48.4568 66.5998Z" fill="white"/>
                                                <path d="M36.3397 55.8027C36.3397 55.8027 24.7626 56.805 23.4966 60.8354C22.6652 63.49 26.9497 63.3481 29.716 60.5276C30.275 59.4466 36.3397 55.8027 36.3397 55.8027Z" fill="white"/>
                                                <path d="M39.7388 55.8027C39.7388 55.8027 51.3146 56.805 52.5798 60.8354C53.4128 63.49 49.128 63.3481 46.3604 60.5276C45.8022 59.4466 39.7388 55.8027 39.7388 55.8027Z" fill="#738DE0"/>
                                                <path d="M31.3553 67.6511C32.1172 67.6511 32.7348 67.0332 32.7348 66.2709C32.7348 65.5086 32.1172 64.8906 31.3553 64.8906C30.5934 64.8906 29.9758 65.5086 29.9758 66.2709C29.9758 67.0332 30.5934 67.6511 31.3553 67.6511Z" fill="#738DE0"/>
                                                <path d="M44.6829 68.7967C45.4448 68.7967 46.0624 68.1787 46.0624 67.4164C46.0624 66.6541 45.4448 66.0361 44.6829 66.0361C43.9211 66.0361 43.3035 66.6541 43.3035 67.4164C43.3035 68.1787 43.9211 68.7967 44.6829 68.7967Z" fill="#738DE0"/>
                                                <path d="M42.6221 70.5511C42.4689 72.338 40.5213 73.628 37.5432 73.3732C34.5651 73.1183 32.9639 71.5199 33.1215 69.7335C33.279 67.9471 35.0618 67.3554 38.0386 67.6116C41.0154 67.8678 42.7768 68.7644 42.6221 70.5511Z" fill="#FF97C9"/>
                                                <g >
                                                    <path d="M36.1745 69.4062C36.2061 69.0374 35.9196 68.7117 35.5345 68.6786C35.1493 68.6455 34.8115 68.9176 34.7798 69.2864C34.7481 69.6552 35.0346 69.9809 35.4198 70.014C35.8049 70.0471 36.1428 69.775 36.1745 69.4062Z" fill="#FF97C9"/>
                                                </g>
                                                <g >
                                                    <path d="M40.9384 69.8164C40.9701 69.4476 40.6835 69.1218 40.2984 69.0887C39.9133 69.0556 39.5754 69.3278 39.5437 69.6965C39.512 70.0653 39.7985 70.3911 40.1837 70.4242C40.5688 70.4572 40.9067 70.1851 40.9384 69.8164Z" fill="#FF97C9"/>
                                                </g>
                                                <path d="M39.119 55.7324C39.119 55.7324 37.1134 59.4193 41.6607 62.1396C45.0624 64.1729 48.0074 62.7339 48.0074 62.7339C48.0074 62.7339 46.7552 56.3222 39.119 55.7324Z" fill="#738DE0"/>
                                                <path d="M59.3086 76.1901C60.7139 76.3333 61.5325 79.2256 60.3346 80.1376C59.1367 81.0496 57.1332 79.4846 57.0101 78.1646C56.887 76.8446 57.5253 76.0026 59.3086 76.1901Z" fill="#738DE0"/>
                                                <path d="M43.959 78.0898C44.4513 76.6107 47.823 76.9159 49.3368 78.2873C50.8507 79.6587 51.6093 82.5167 50.8522 83.0552C50.0952 83.5936 48.5305 81.8695 47.1315 81.3671C45.7324 80.8647 43.4808 79.5214 43.959 78.0898Z" fill="#738DE0"/>
                                                <g  opacity="0.42">
                                                    <path d="M58.5478 8.43889C58.9995 6.6502 56.0432 4.36097 51.9445 3.32575C47.8459 2.29052 44.157 2.90132 43.7052 4.69001C43.2534 6.47869 46.2098 8.76793 50.3085 9.80315C54.4071 10.8384 58.096 10.2276 58.5478 8.43889Z" fill="white"/>
                                                </g>
                                            </svg>
                                            <p className="text-sm leading-sm text-utility-gray-400 font-normal">
                                                Hiện chưa có video nào
                                            </p>
                                        </div>
                                        :
                                        <div className="gap-y-lg flex flex-col py-lg px-xl cursor-pointer">
                                            {
                                                videos.slice(0, 5).map((video, index) => (
                                                    <div
                                                        key={index}
                                                        onClick={() => onClickVideo(video)}
                                                        className="overflow-hidden hover:scale-[1.02] transition-transform gap-x-md flex flex-row"
                                                    >
                                                        <div className="relative h-[97px] aspect-[155.00/96.88]">
                                                            <img
                                                                src={video.thumbnail}
                                                                alt={video.title}
                                                                className="w-full h-full object-cover rounded-xl "
                                                            />
                                                            <div className="absolute inset-0 flex justify-center items-center bg-black bg-opacity-30">
                                                                <FaPlay className="text-white text-4xl opacity-70 color-fa-play" />
                                                            </div>
                                                        </div>
                                                        <div className="bg-white sm:h-[64px] gap-y-xs">
                                                            <p className="text-primary-900 line-clamp-3 font-semibold text-sm leading-sm">{video.title}</p>
                                                            <p className="text-tertiary-600 text-sm font-normal leading-sm">
                                                                {new Date(video.updatedAt).toLocaleDateString('vi-VN')}
                                                            </p>
                                                        </div>
                                                    </div>
                                                ))
                                            }
                                        </div>
                                }
                            </div>
                        </div>
                    </div>
                    {/*Video liên quan*/}
                    {
                        screenSize.lte960 ?
                            <div className="flex flex-col mt-4xl mb-7xl md:flex-row md:mb-4xl">
                                <div className="w-full md:w-3/4 flex flex-col">
                                    {/* Related videos */}
                                    <div className="flex justify-between flex-row mb-3xl">
                                        <p className="text-primary-900 font-semibold text-lg leading-lg">Bài livestream cũ</p>
                                        <p className={clsx(
                                            "text-tertiary-fg font-semibold text-sm leading-sm cursor-pointer",
                                            videos.length > 3 ? '': 'hidden'
                                        )} onClick={clickBack}>Xem thêm</p>
                                    </div>

                                    {
                                        videos.length === 0 ?
                                            <div className="gap-y-lg flex flex-col py-2xl px-xl cursor-pointer items-center justify-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="96" height="120" viewBox="0 0 96 120" fill="none">
                                                    <path opacity="0.27" d="M32.9312 16.873L1.04278 104.795C0.577715 106.075 0.623139 107.485 1.16963 108.732C1.71612 109.979 2.72172 110.968 3.97782 111.493C11.3735 114.597 27.2411 119.999 48.1561 119.999C69.071 119.999 84.9345 114.599 92.3359 111.493C93.5918 110.967 94.5971 109.978 95.1433 108.731C95.6896 107.484 95.7348 106.075 95.2696 104.795L63.3812 16.873H32.9312Z" fill="#FF97C9"/>
                                                    <path d="M14.5134 27.0729C16.1534 27.0729 17.4829 25.7428 17.4829 24.1019C17.4829 22.461 16.1534 21.1309 14.5134 21.1309C12.8734 21.1309 11.5439 22.461 11.5439 24.1019C11.5439 25.7428 12.8734 27.0729 14.5134 27.0729Z" fill="#738DE0"/>
                                                    <path d="M81.7988 27.0729C83.4388 27.0729 84.7683 25.7428 84.7683 24.1019C84.7683 22.461 83.4388 21.1309 81.7988 21.1309C80.1588 21.1309 78.8293 22.461 78.8293 24.1019C78.8293 25.7428 80.1588 27.0729 81.7988 27.0729Z" fill="#738DE0"/>
                                                    <path d="M48.1562 31.9519C49.7962 31.9519 51.1257 30.6217 51.1257 28.9808C51.1257 27.3399 49.7962 26.0098 48.1562 26.0098C46.5162 26.0098 45.1868 27.3399 45.1868 28.9808C45.1868 30.6217 46.5162 31.9519 48.1562 31.9519Z" fill="#738DE0"/>
                                                    <path d="M87.9288 18.3413C87.9288 23.894 71.8094 28.394 47.9878 28.394C24.1663 28.394 8.38965 23.8937 8.38965 18.3413C8.38965 12.789 24.1584 8.29004 47.9816 8.29004C71.8047 8.29004 87.9288 12.79 87.9288 18.3413Z" fill="#99ADF9"/>
                                                    <g  opacity="0.42">
                                                        <path d="M87.9298 14.6727C87.9298 20.2238 71.8105 24.7253 47.9889 24.7253C24.1673 24.7253 8.39697 20.2238 8.39697 14.6727C8.39697 9.12168 24.1658 4.62012 47.9889 4.62012C71.812 4.62012 87.9298 9.12168 87.9298 14.6727Z" fill="white"/>
                                                    </g>
                                                    <path d="M48.1973 18.0477C54.5242 18.0823 60.8305 17.3232 66.9685 15.7883C65.7554 6.88696 57.471 0 47.4306 0C37.5419 0 29.3563 6.68217 27.9539 15.3913C33.8555 17.077 40.7847 18.0477 48.1973 18.0477Z" fill="#99ADF9"/>
                                                    <path d="M63.5846 86.2754C63.7377 84.7991 62.3337 84.082 59.9381 83.9114C57.5426 83.7407 56.0929 84.2523 55.9398 85.7283C55.885 86.2388 56.0106 86.7523 56.2946 87.18C56.3467 87.2619 56.3744 87.3569 56.3744 87.454C56.3744 87.551 56.3467 87.646 56.2946 87.7279C56.0687 88.0936 55.8926 88.4713 56.0214 88.6164C56.2074 88.8251 56.6475 88.6425 57.1521 88.2971C57.2232 88.2488 57.3056 88.2197 57.3913 88.2124C57.477 88.2052 57.5631 88.22 57.6414 88.2556C57.9777 88.3999 58.3291 88.5061 58.6891 88.572C58.7889 88.59 58.8811 88.6371 58.9541 88.7074C59.0272 88.7777 59.0779 88.868 59.0997 88.967C59.1855 89.3967 59.3559 89.826 59.695 89.854C60.1244 89.8941 60.3472 89.5474 60.4307 89.0393C60.4461 88.9323 60.4953 88.833 60.5709 88.7557C60.6465 88.6785 60.7448 88.6273 60.8514 88.6096C61.0362 88.5807 61.2191 88.541 61.3993 88.4907C61.4852 88.4687 61.5753 88.4698 61.6607 88.4936C61.7461 88.5175 61.8237 88.5633 61.8858 88.6266C62.2951 89.026 62.6744 89.2393 62.8961 89.0562C63.0394 88.9344 62.952 88.5551 62.8132 88.1972C62.7762 88.105 62.7665 88.0042 62.7852 87.9066C62.804 87.8091 62.8505 87.7191 62.9191 87.6473C63.293 87.2807 63.5282 86.7959 63.5846 86.2754Z" fill="#FF97C9"/>
                                                    <path d="M32.1181 73.4054C30.4639 77.128 30.1146 84.5047 29.8699 84.9953C29.6253 85.486 27.9199 86.3704 28.3559 87.268C28.7566 88.0927 31.5343 89.2167 32.3628 88.9861C33.6507 88.6368 34.923 85.2004 35.7059 83.0242C38.5679 84.7423 43.3535 85.3238 43.3535 85.3238C43.3535 85.3238 42.8678 88.8946 42.4947 89.6764C42.1217 90.4582 40.8147 91.4133 41.0837 92.3667C41.3526 93.3202 43.9316 93.8587 45.0763 94.006C46.221 94.1534 48.0745 87.8665 48.5026 85.6658C56.1173 86.4719 65.3091 85.8862 65.3091 85.8862C65.3091 85.8862 66.4097 89.7249 65.3333 91.4373C64.8424 92.2199 64.5306 93.8714 64.7952 93.9815C66.4337 94.6415 68.546 94.2685 69.22 93.7854C69.9784 93.2469 72.6432 80.859 71.0289 76.1829C69.4146 71.5068 66.2263 68.8264 60.615 67.4088C55.0037 65.9913 46.5346 65.9258 46.5346 65.9258L32.1181 73.4054Z" fill="white"/>
                                                    <path d="M59.3587 65.9326C59.3587 65.9326 58.0708 70.8237 60.6795 73.4052C63.2882 75.9868 68.6289 75.6573 71.5282 72.2811C68.7536 67.2197 59.3587 65.9326 59.3587 65.9326Z" fill="#738DE0"/>
                                                    <path d="M28.8512 79.875C28.8512 79.875 33.7816 81.6547 35.086 86.4054C32.1509 91.0473 27.6118 89.6297 27.6118 89.6297" fill="#738DE0"/>
                                                    <path d="M65.2502 75.1099C66.7656 75.1099 67.8662 75.6196 68.526 76.5919C69.0282 77.3322 70.5052 79.1291 72.1552 78.1396C74.3147 76.8509 70.9946 72.3666 70.9946 72.3666L66.8629 72.0488L65.2502 75.1099Z" fill="#738DE0"/>
                                                    <path d="M31.7031 74.5277C31.7031 74.5277 34.136 80.1424 40.2422 80.1374C45.5916 80.1374 48.9727 74.0065 47.3475 69.7598C42.0998 71.896 31.7031 74.5277 31.7031 74.5277Z" fill="#F0F4FF"/>
                                                    <path d="M30.4292 57.827C30.4292 57.827 28.9422 54.3606 29.8769 52.9101C30.8116 51.4597 33.8354 56.5798 33.8354 56.5798L30.4292 57.827Z" fill="#FEC272"/>
                                                    <path d="M46.4892 57.827C46.4892 57.827 47.973 54.3606 47.0386 52.9101C46.1042 51.4597 43.0789 56.5798 43.0789 56.5798L46.4892 57.827Z" fill="#FEC272"/>
                                                    <path d="M48.4568 66.5998C47.983 72.1193 46.1329 76.8728 37.2615 76.1098C28.3902 75.3468 27.2269 70.3347 27.7007 64.8157C28.1744 59.2968 33.2058 55.2228 38.9374 55.714C44.6689 56.2052 48.9305 61.0803 48.4568 66.5998Z" fill="white"/>
                                                    <path d="M36.3397 55.8027C36.3397 55.8027 24.7626 56.805 23.4966 60.8354C22.6652 63.49 26.9497 63.3481 29.716 60.5276C30.275 59.4466 36.3397 55.8027 36.3397 55.8027Z" fill="white"/>
                                                    <path d="M39.7388 55.8027C39.7388 55.8027 51.3146 56.805 52.5798 60.8354C53.4128 63.49 49.128 63.3481 46.3604 60.5276C45.8022 59.4466 39.7388 55.8027 39.7388 55.8027Z" fill="#738DE0"/>
                                                    <path d="M31.3553 67.6511C32.1172 67.6511 32.7348 67.0332 32.7348 66.2709C32.7348 65.5086 32.1172 64.8906 31.3553 64.8906C30.5934 64.8906 29.9758 65.5086 29.9758 66.2709C29.9758 67.0332 30.5934 67.6511 31.3553 67.6511Z" fill="#738DE0"/>
                                                    <path d="M44.6829 68.7967C45.4448 68.7967 46.0624 68.1787 46.0624 67.4164C46.0624 66.6541 45.4448 66.0361 44.6829 66.0361C43.9211 66.0361 43.3035 66.6541 43.3035 67.4164C43.3035 68.1787 43.9211 68.7967 44.6829 68.7967Z" fill="#738DE0"/>
                                                    <path d="M42.6221 70.5511C42.4689 72.338 40.5213 73.628 37.5432 73.3732C34.5651 73.1183 32.9639 71.5199 33.1215 69.7335C33.279 67.9471 35.0618 67.3554 38.0386 67.6116C41.0154 67.8678 42.7768 68.7644 42.6221 70.5511Z" fill="#FF97C9"/>
                                                    <g >
                                                        <path d="M36.1745 69.4062C36.2061 69.0374 35.9196 68.7117 35.5345 68.6786C35.1493 68.6455 34.8115 68.9176 34.7798 69.2864C34.7481 69.6552 35.0346 69.9809 35.4198 70.014C35.8049 70.0471 36.1428 69.775 36.1745 69.4062Z" fill="#FF97C9"/>
                                                    </g>
                                                    <g >
                                                        <path d="M40.9384 69.8164C40.9701 69.4476 40.6835 69.1218 40.2984 69.0887C39.9133 69.0556 39.5754 69.3278 39.5437 69.6965C39.512 70.0653 39.7985 70.3911 40.1837 70.4242C40.5688 70.4572 40.9067 70.1851 40.9384 69.8164Z" fill="#FF97C9"/>
                                                    </g>
                                                    <path d="M39.119 55.7324C39.119 55.7324 37.1134 59.4193 41.6607 62.1396C45.0624 64.1729 48.0074 62.7339 48.0074 62.7339C48.0074 62.7339 46.7552 56.3222 39.119 55.7324Z" fill="#738DE0"/>
                                                    <path d="M59.3086 76.1901C60.7139 76.3333 61.5325 79.2256 60.3346 80.1376C59.1367 81.0496 57.1332 79.4846 57.0101 78.1646C56.887 76.8446 57.5253 76.0026 59.3086 76.1901Z" fill="#738DE0"/>
                                                    <path d="M43.959 78.0898C44.4513 76.6107 47.823 76.9159 49.3368 78.2873C50.8507 79.6587 51.6093 82.5167 50.8522 83.0552C50.0952 83.5936 48.5305 81.8695 47.1315 81.3671C45.7324 80.8647 43.4808 79.5214 43.959 78.0898Z" fill="#738DE0"/>
                                                    <g  opacity="0.42">
                                                        <path d="M58.5478 8.43889C58.9995 6.6502 56.0432 4.36097 51.9445 3.32575C47.8459 2.29052 44.157 2.90132 43.7052 4.69001C43.2534 6.47869 46.2098 8.76793 50.3085 9.80315C54.4071 10.8384 58.096 10.2276 58.5478 8.43889Z" fill="white"/>
                                                    </g>
                                                </svg>
                                                <p className="text-sm leading-sm text-utility-gray-400 font-normal">
                                                    Hiện chưa có video nào
                                                </p>
                                            </div>
                                            :
                                            <div className="grid gap-xl grid-cols-0 sm:grid-cols-3 cursor-pointer">
                                                {videos.slice(0, 3).map((video, index) => (
                                                    <div
                                                        key={index}
                                                        onClick={() => onClickVideo(video)}
                                                        className="overflow-hidden hover:scale-[1.02] transition-transform"
                                                    >
                                                        <div className="relative w-full mb-lg">
                                                            <img
                                                                src={video.thumbnail}
                                                                alt={video.title}
                                                                className="w-full h-full object-cover aspect-[343.00/214.38] rounded-xl "
                                                            />
                                                            <div className="absolute inset-0 flex justify-center items-center bg-black bg-opacity-30">
                                                                <FaPlay className="text-white text-4xl opacity-70 color-fa-play" />
                                                            </div>
                                                        </div>
                                                        <div className="px-xs py-none bg-white sm:h-[64px]">
                                                            <p className="text-primary-900 line-clamp-2 font-semibold text-sm leading-sm">{video.title}</p>
                                                            <p className="text-tertiary-600 text-sm font-normal leading-sm">
                                                                {new Date(video.updatedAt).toLocaleDateString('vi-VN')}
                                                            </p>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                    }
                                </div>
                            </div>
                            :
                            ''
                    }
                </div>
            </div>
            );
        }else {
            return "";
        }
    }
}
