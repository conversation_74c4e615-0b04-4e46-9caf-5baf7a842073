"use client";

import DashboardLayout from "../../../components/layouts/DashboardLayout";
import CountdownWidget from "../../../components/dashboard/CountdownWidget";
import React, {useEffect, useRef, useState} from "react";
import strapi from "@/app/api/strapi";
import Cookies from "universal-cookie";
import {FaPlay, FaSearch} from 'react-icons/fa';
import TextField from "@/components/TextField";
import {useRouter} from "next/navigation";
import {useDashboardLayout} from "@/context/DashboardLayoutContext";


export default function ViewVideo() {
    const router = useRouter();
    const [videos, setListVideos] = useState([]);
    const [videoOriginal, setListVideoOriginal] = useState([]);
    // const [keySearch, setKeySearch] = useState("");
    const [formData, setFormData] = useState({keySearch: ""});
    const [errors, setErrors] = useState({});
    const timeoutRef = useRef(null);
    const {setTitle, keySearch, setKeySearch, setIsSearch, setIsDetail, setIsTurnLive} = useDashboardLayout();

    useEffect(() => {
        setTitle("Xem video");
        setIsSearch(true);
        setIsDetail(false);
        setIsTurnLive(false);
        setKeySearch(keySearch);
        return () => {
            setIsSearch(false);
            setIsTurnLive(false);
        }
    }, []);

    useEffect(() => {

        const cookies = new Cookies();
        const user_data = cookies.get('user_data');
        const getListVideo = async () => {
            await strapi.users.getUserById(user_data.id).then(async res => {
                if (res && res.data && res.data.orders[0].course.collection_video_id) {
                    await strapi.quanLy.getVideoUpload(res.data.orders[0].course.collection_video_id).then(res => {
                        if (res.data && res.data.length > 0) {
                            setListVideos(res.data);
                            setListVideoOriginal(res.data);
                        } else {
                            setListVideos([]);
                            setListVideoOriginal([]);
                        }
                    });
                } else {
                }
            });
        }
        getListVideo();

    }, []);

    const onClickVideo = (video) => {
        router.push(window.location.href + '/chi-tiet?videoId=' + video.video_id + '&collectionId=' + video.collection_id);
    }
    useEffect(() => {
        if (!videoOriginal || videoOriginal.length === 0) return;
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }
        timeoutRef.current = setTimeout(() => {
            if (keySearch !== "") {
                const keySearch_ = keySearch.trim().toLowerCase();
                const matchedVideos = videoOriginal.filter(video =>
                    video.title.toLowerCase().includes(keySearch_) ||
                    video.description.toLowerCase().includes(keySearch_)
                );
                setListVideos(matchedVideos);
            } else {
                setListVideos(videoOriginal);
            }

        }, 300);
        return () => clearTimeout(timeoutRef.current);

    }, [keySearch, videoOriginal])

    return (
        // <DashboardLayout title="Xem video" keySearch={keySearch} setKeySearch={setKeySearch} isSearch={true} isDetail={false} isTurnLive={true}>
        //     {/*<div className="group hover:bg-brand-solid p-4">*/}
        //     {/*    <p className="text-gray-500 group-hover:text-error-primary-600">Tôi đổi màu khi bạn hover thẻ cha</p>*/}
        //     {/*</div>*/}
        //
        // </DashboardLayout>

        <div className="flex flex-col h-full min-h-[calc(100vh-160px)]">
            <div className="bg-white w-full sm:gap-4xl flex flex-col xs:gap-y-3xl">
                {/* Video đầu tiên */}
                {videos.length > 0 && (
                    <div className="flex flex-col xs:gap-lg sm:flex-row sm:gap-xl cursor-pointer" onClick={() => onClickVideo(videos[0])}>
                        <div className="relative w-full md:w-1/2 rounded-md">
                            <img
                                src={videos[0].thumbnail}
                                alt="Video Thumbnail"
                                className="w-full h-full object-cover rounded-md aspect-[296.5/185.31]"
                            />
                            <div
                                className="absolute inset-0 flex flex-col justify-center items-center bg-black/30">
                                <button className="text-white/65 text-4xl">
                                    <FaPlay className="color-fa-play"/>
                                </button>
                            </div>
                            <div
                                className="absolute top-2 left-2 py-xs px-lg rounded-full border bg-utility-brand-50 border-utility-brand-200">
                                <p className="text-utility-brand-700 text-sm font-medium">Mới nhất</p>
                            </div>
                        </div>
                        <div className="flex flex-col gap-md w-full md:w-1/2">
                            <p className="text-primary-900 font-semibold xs:text-sm xs:leading-sm sm:text-md sm:leading-md md:text-lg md:leading-lg">{videos[0].title}</p>
                            <p className="text-tertiary-600 text-sm font-normal">{new Date(videos[0].updatedAt).toLocaleDateString("vi-VN")}</p>
                            <p className="text-tertiary-600 xs:text-sm font-normal xs:hidden sm:block md:text-md md:leading-md">{videos[0].description}</p>
                        </div>
                    </div>
                )}

                {/* Các video còn lại */}
                <div
                    className=" grid cursor-pointer xs:gap-x-xl xs:gap-y-3xl xs:grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 sm:gap-y-4xl">
                    {videos.slice(1).map((video, index) => (
                        <div
                            key={index}
                            onClick={() => onClickVideo(video)}
                            className="rounded-md overflow-hidde"
                        >
                            <div className="relative w-full mb-lg">
                                <img
                                    src={video.thumbnail}
                                    alt="Video Thumbnail"
                                    className="w-full h-full object-cover rounded-md aspect-[8/5]"
                                />
                                <div className="absolute inset-0 flex flex-col justify-center items-center bg-black/30">
                                    <button className="text-white/65 text-4xl">
                                        <FaPlay className="color-fa-play"/>
                                    </button>
                                </div>
                            </div>
                            <div className="pl-xs">
                                <p className="text-primary-900 font-semibold line-clamp-2 xs:text-sm xs:leading-sm xs:mb-xs">{video.title}</p>
                                <p className="text-tertiary-600 font-normal xs:text-sm xs:leading-sm">{new Date(video.updatedAt).toLocaleDateString("vi-VN")}</p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>

    );
}
