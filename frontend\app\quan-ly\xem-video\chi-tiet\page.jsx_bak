"use client";

import DashboardLayout from "../../../../components/layouts/DashboardLayout";
import CountdownWidget from "../../../../components/dashboard/CountdownWidget";
import React, {Suspense, useEffect, useRef, useState} from "react";
import strapi from "@/app/api/strapi";
import Cookies from "universal-cookie";
import { FaPlay } from 'react-icons/fa';
import TextField from "@/components/TextField";
import {useParams, useRouter, useSearchParams} from "next/navigation";
import Hls from "hls.js";
import Button from "@/components/Button";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import ArrowLeftIcon from "@/components/icons/ArrowLeftIcon";
import LineDivider from "@/components/icons/LineDivider";
import FileTextIcon from "@/components/icons/FileTextIcon";
import ArrowDownIcon from "@/components/icons/ArrowDownIcon";
import ChevronDownIcon from "@/components/icons/ChevronDownIcon";
import ChevronUpIcon from "@/components/icons/ChevronUpIcon";
import {useNotification} from "@/context/NotificationContext";
import {useDashboardLayout} from "@/context/DashboardLayoutContext";


export default function viewDetailVideo() {
    return (
        <Suspense fallback={<div>Đang tải...</div>}>
            <ViewDetailContent />
        </Suspense>
    );
    function ViewDetailContent() {
        const router = useRouter();
        const searchParams = useSearchParams();
        const videoId = searchParams.get('videoId');
        const collectionId = searchParams.get('collectionId');
        const videoRef = useRef(null);
        const [videoUrl, setVideoUrl] = useState("");
        const [src, setSrc] = useState("");
        const [formData, setFormData] = useState({});
        const [videos, setListVideos] = useState([]);
        const [isExpandChapter, setIsExpandChapter] = useState(false);
        const [isExpandExercise, setIsExpandExercise] = useState(false);
        const {showNotification} = useNotification();
        const [chapters, setListChapters] = useState([
            { time: '00:00:00', label: 'Thành phần nguyên tử' },
            { time: '00:36:12', label: 'Cấu tạo nguyên tử' },
            { time: '00:46:39', label: 'Khối lượng, kích thước nguyên tử' },
            { time: '00:50:42', label: 'Nguyên tử khối và nguyên tử khối trung bình...' },
            { time: '01:20:17', label: 'Khối lượng, kích thước nguyên tử' },
            { time: '01:20:17', label: 'Khối lượng, kích thước nguyên tử' },
            { time: '01:20:17', label: 'Khối lượng, kích thước nguyên tử' },
            { time: '01:20:17', label: 'Khối lượng, kích thước nguyên tử' },
        ]);
        const [exercise, setListExercise] = useState([
            { label: 'Tổng hợp', link: '#' },
            { label: 'Thành phần nguyên tử', link: '#' },
            { label: 'Cấu tạo nguyên tử', link: '#' },
            { label: 'Khối lượng, kích thước nguyên tử', link: '#' },
            { label: 'Khối lượng, kích thước nguyên tử', link: '#' },
            { label: 'Khối lượng, kích thước nguyên tử', link: '#' },
            { label: 'Khối lượng, kích thước nguyên tử', link: '#' },
            { label: 'Khối lượng, kích thước nguyên tử', link: '#' },
        ]);

        const {setTitle, keySearch, setKeySearch, setIsSearch, setIsDetail, setIsTurnLive} = useDashboardLayout();

        useEffect(() => {
            setTitle("");
            setIsSearch(false);
            setIsDetail(true);
            setIsTurnLive(false);
            setKeySearch(keySearch);
            return () => {
                setIsSearch(false);
                setIsTurnLive(false);
                setIsDetail(false);
            }
        }, []);


        useEffect(() => {
            const getVideo = async () => {
                const data = { videoId: videoId};
                try {
                    const res = await strapi.bunny.getSrcVideoPlay(data);
                    setSrc(res.data.iframeUrl);
                    setFormData({title: res.data.title, description:  res.data.description, createdAt: res.data.createdAt, documentFile: res.data.documentFile});
                } catch (error) {
                    console.error("Lỗi khi lấy video:", error);
                }
            };
            const getListVideo = async () => {
                await strapi.quanLy.getVideoUpload(collectionId).then(res => {
                    if (res.data && res.data.length > 0) {
                        setListVideos(res.data);
                        let a = res.data.filter(video => video.video_id !== videoId);
                        setListVideos(a);
                    }else {
                        setListVideos([]);
                    }
                });
            }
            if (collectionId) {
                getListVideo();
            }
            if (videoId) {
                getVideo();
            }
        }, [videoId]);
        const clickBack = () => {
            router.push("/quan-ly/xem-video");
        }
        const onClickVideo = (video) => {
            router.push( '/quan-ly/xem-video/chi-tiet?videoId=' + video.video_id + '&collectionId=' + video.collection_id);
        }
        const handleDownload = async () => {
            const documentFile = formData.documentFile;
            if ( !documentFile || !documentFile?.url) {
                showNotification({
                    type: "error",
                    title: "Không có file",
                    message:  "Không có file để download",
                    duration: 5000,
                });
                return;
            }
            // Tạo URL đầy đủ

            const fullUrl =  process.env.NEXT_PUBLIC_STRAPI_URL + documentFile.url;
            try {
                const response = await fetch(fullUrl);
                const blob = await response.blob();
                const link = document.createElement("a");
                link.href = URL.createObjectURL(blob);
                link.download = documentFile.name || 'tai-lieu.pdf';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(link.href);
            } catch (error) {
                console.error(error);
                showNotification({
                    type: "error",
                    title: "Lỗi tải file",
                    message: "Có lỗi xảy ra khi tải file",
                    duration: 5000,
                });
            }
        }
        if (src) {
            return (
                // <DashboardLayout isDetail={true}>
                //
                // </DashboardLayout>

            <div className="flex flex-col min-h-[calc(100vh-160px)] bg-gray-50">
                {/* Main content + Sidebar */}
                <div className="flex flex-col">
                    {/*Video + mục lục + bài tập liên quan*/}
                    <div className="flex flex-col md:flex-row">
                        {/*Video*/}
                        <div className="flex flex-col md:flex-1">
                            {/* Video iframe */}
                            <div className="relative w-full overflow-hidden shadow-md md:rounded-md xs:rounded-none aspect-video">
                                <iframe
                                    src={src}
                                    loading="lazy"
                                    className="absolute top-0 left-0 w-full h-full border-0 md:rounded-lg"
                                    allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture"
                                    allowFullScreen
                                    title="Video Player"
                                ></iframe>
                            </div>

                            {/* Video info */}
                            <div className="bg-white xs:mt-2xl md:mt-[20px] mb-4xl flex flex-row gap-[55px] relative">
                                <div className="w-full md:w-3/4">
                                    <div className="flex justify-between items-center">
                                        <p className="text-primary-900 xs:text-lg leading-lg md:text-xl font-semibold md:leading-xl">{formData.title}</p>
                                    </div>
                                    <div className="mt-lg mb-lg flex flex-row justify-between">
                                        <p className= "text-tertiary-600 text-sm font-normal leading-sm">
                                            {new Date(formData.createdAt).toLocaleDateString('vi-VN')}
                                        </p>
                                        <div className="w-full md:hidden flex justify-end">
                                            <button onClick={handleDownload} className="flex items-center rounded-md py-md px-lg bg-white border button-secondary-border shadow-skeu-var">
                                                <ArrowDownIcon></ArrowDownIcon>
                                                <p className="ml-xs text-sm font-semibold text-tertiary-fg leading-sm">Tải tài liệu</p>
                                            </button>
                                        </div>
                                    </div>

                                    <p className="text-tertiary-600 text-sm leading-sm md:text-md md:leading-md font-normal">{formData.description}</p>
                                </div>
                                <div className="w-1/4 relative hidden md:block">
                                    <button onClick={handleDownload} className="flex items-center rounded-md min-w-[124px] py-[10px] px-[14px] bg-white border button-secondary-border shadow-skeu-var absolute top-0 right-0">
                                        <ArrowDownIcon></ArrowDownIcon>
                                        <p className="ml-xs text-sm font-semibold text-tertiary-fg leading-sm">Tải tài liệu</p>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {/*Mục lục + bài tập liên quan*/}
                        <div className="w-[311px] md:ml-xl flex flex-col xs:w-full md:w-[314px]">
                            {/*md:w-[311]*/}
                            {/* Mục lục */}
                            <div className="bg-white flex mb-xl flex-col rounded-lg border border-secondary gap-0">
                                <p className="pt-lg pr-2xl pb-0 pl-2xl mb-xl text-primary-900 text-lg font-semibold leading-lg">Mục lục</p>
                                <LineDivider></LineDivider>
                                <ul className="">

                                    {(isExpandChapter ? chapters : chapters.slice(0, 5)).map(({ time, label }, idx) => (
                                        <li key={idx} hidden={isExpandChapter} className="pt-lg pb-lg pr-2xl pl-2xl flex items-start ">
                                            <a
                                                href="#"
                                                className="bg-utility-blue-50 rounded-xs min-w-[64px] pt-xxs pb-xxs font-medium text-center text-xs leading-xs text-utility-blue-600 mr-sm"
                                            >
                                                {time}
                                            </a>{' '}
                                            <p className="overflow-ellipsis text-primary-900 text-sm font-medium leading-sm line-clamp-1"> {label}</p>
                                        </li>
                                    ))}
                                </ul>
                                {
                                    chapters.length > 5 ? <div className="w-full flex items-center justify-center gap-2 p-xl">
                                        <p className="text-tertiary-fg text-sm leading-sm font-semibold cursor-pointer" onClick={() => setIsExpandChapter(!isExpandChapter)}>{isExpandChapter ? 'Thu gọn' : 'Mở rộng' }</p>
                                        {isExpandChapter ? <ChevronUpIcon></ChevronUpIcon> : <ChevronDownIcon></ChevronDownIcon>}
                                    </div> : ''
                                }

                            </div>
                            {/* Bài tập liên quan */}
                            <div className="bg-white flex flex-col rounded-lg border border-secondary gap-0">
                                <p className="pt-lg pr-2xl pb-0 pl-2xl text-primary-900 text-lg font-semibold leading-lg">Bài tập liên quan</p>
                                <p className="pl-2xl text-tertiary-600 text-sm mb-xl leading-sm font-normal line-clamp-1">Chọn bài tập bạn muốn làm</p>
                                <LineDivider></LineDivider>
                                <ul className="">
                                    {(isExpandExercise? exercise: exercise.slice(0, 5)).map((value, idx) => (
                                        <li key={idx} className="pt-lg pb-lg pr-2xl pl-2xl flex items-start">
                                            <FileTextIcon></FileTextIcon>
                                            <a href={value.link} className="ml-sm overflow-ellipsis text-primary-900 text-sm font-medium leading-sm line-clamp-1"> {value.label}</a>
                                        </li>
                                    ))}
                                </ul>
                                {
                                    exercise.length > 5 ? <div className="w-full flex items-center justify-center gap-2 p-xl">
                                        <p className="text-tertiary-fg text-sm leading-sm font-semibold cursor-pointer" onClick={() => setIsExpandExercise(!isExpandExercise)}>{isExpandExercise ? 'Thu gọn' : 'Mở rộng' }</p>
                                        {isExpandExercise ? <ChevronUpIcon></ChevronUpIcon> : <ChevronDownIcon></ChevronDownIcon>}
                                    </div> : ''
                                }

                            </div>
                        </div>
                    </div>
                    {/*Video liên quan*/}
                    <div className="flex flex-col mt-4xl mb-7xl md:flex-row md:mb-4xl">
                        <div className="w-full md:w-3/4 flex flex-col">
                            {/* Related videos */}
                            <div className="flex justify-between flex-row mb-3xl">
                                <p className="text-primary-900 font-semibold text-lg leading-lg">Bài livestream cũ</p>
                                <p className="text-tertiary-fg font-semibold text-sm leading-sm cursor-pointer" onClick={clickBack}>Xem thêm</p>
                            </div>
                            <div className="grid gap-xl grid-cols-0 sm:grid-cols-3 cursor-pointer">
                                {videos.slice(0, 3).map((video, index) => (
                                    <div
                                        key={index}
                                        onClick={() => onClickVideo(video)}
                                        className="overflow-hidden hover:scale-[1.02] transition-transform"
                                    >
                                        <div className="relative w-full mb-lg">
                                            <img
                                                src={video.thumbnail}
                                                alt={video.title}
                                                className="w-full h-full object-cover aspect-[343.00/214.38] rounded-xl "
                                            />
                                            <div className="absolute inset-0 flex justify-center items-center bg-black bg-opacity-30">
                                                <FaPlay className="text-white text-4xl opacity-70 color-fa-play" />
                                            </div>
                                        </div>
                                        <div className="px-xs py-none bg-white sm:h-[64px]">
                                            <p className="text-primary-900 line-clamp-2 font-semibold text-sm leading-sm">{video.title}</p>
                                            <p className="text-tertiary-600 text-sm font-normal leading-sm">
                                                {new Date(video.updatedAt).toLocaleDateString('vi-VN')}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            );
        }else {
            return "";
        }
    }
}
