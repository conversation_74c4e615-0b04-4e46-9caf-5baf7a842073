/**
 * upload-video controller
 */

// import { factories } from '@strapi/strapi'

// export default factories.createCoreController('api::upload-video.upload-video');

import { factories } from '@strapi/strapi';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';
import axios from "axios";
import FormData from 'form-data';


const BUNNY_API_KEY = process.env.BUNNY_API_KEY;
const BUNNY_LIBRARY_ID = process.env.BUNNY_LIBRARY_ID;
const BUNNY_BASE_URL = process.env.BUNNY_BASE_URL;
const DOMAIN = process.env.DOMAIN;


export default factories.createCoreController('api::upload-video.upload-video', ({ strapi }) => ({
    async upload(ctx) {
        return new Promise(async (resolve, reject) => {
            try {
                const {files} = ctx.request as any;

                let body = ctx.request.body;
                const file = files.file;
                if (!file) {
                    return ctx.badRequest('Không tìm thấy file');
                }

                let  videoId = '';
                let filePath = "";
                let collectionId = "";
                if (body && body.collection.length > 0 ) {
                    for (let i = 0; i < body.collection.length; i++) {
                        collectionId = body.collection[i];
                        if (!collectionId) {
                            continue;
                        }
                        // Upload lên Bunny Stream
                        const uploadResponse = await axios.post(
                            `https://video.bunnycdn.com/library/${BUNNY_LIBRARY_ID}/videos`,
                            {title: file.originalFilename , collectionId},
                            {
                                headers: {
                                    AccessKey: BUNNY_API_KEY,
                                    'Content-Type': 'application/json',
                                },
                            }
                        );
                        videoId = uploadResponse.data.guid;
                        console.log("videoId" , videoId);
                        // Upload actual video file to Bunny
                        // const fileStream = fs.createReadStream(destinationPath);
                        const fileStream = fs.createReadStream(file.filepath);
                        await axios.put(
                            `https://video.bunnycdn.com/library/${BUNNY_LIBRARY_ID}/videos/${videoId}`,
                            fileStream,
                            {
                                headers: {
                                    AccessKey: BUNNY_API_KEY,
                                    'Content-Type': file.mimetype,
                                },
                                maxContentLength: Infinity,
                                maxBodyLength: Infinity,
                            }
                        );
                        //upload thumbnail lên video
                        const {files} = ctx.request as any;
                        const thumbnail = files.thumbnail;
                        if (thumbnail) {
                            //upload video lên folder uploads hệ thống
                            const uploadFolderPath = path.join(strapi.dirs.static.public, 'uploads');
                            const destinationPath = path.join(uploadFolderPath, thumbnail.originalFilename);
                            await new Promise<void>((resolve, reject) => {
                                const reader = fs.createReadStream(thumbnail.filepath);
                                const writer = fs.createWriteStream(destinationPath);
                                reader.pipe(writer);
                                writer.on('finish', resolve);
                                writer.on('error', reject);
                            });

                            if (process.env.NODE_ENV === 'development') {
                                filePath = encodeURIComponent("https://admin.ongbadayhoa.com/uploads/small_coffee_art_2b4d14978e.jpeg");
                            }else {
                                filePath = encodeURIComponent(DOMAIN + "/uploads/" + thumbnail.originalFilename);
                            }
                            const options = {
                                method: 'POST',
                                url: `https://video.bunnycdn.com/library/${BUNNY_LIBRARY_ID}/videos/${videoId}/thumbnail?thumbnailUrl=${filePath}`,
                                headers: {
                                    accept: 'application/json',
                                    AccessKey: BUNNY_API_KEY,
                                },
                            };
                            axios
                                .request(options)
                                .then(res => console.log(res.data))
                                .catch(err => console.error(err));
                        }
                    }
                }
                let documentId = null;
                if (files && files.document) {
                    const uploadedFile = await strapi.plugins.upload.services.upload.upload({
                        data: {},
                        files: files.document,
                    });

                    if (uploadedFile && uploadedFile.length > 0) {
                        documentId = uploadedFile[0].id;
                    }
                }
                let data = {
                    ...body,
                    video_id: videoId,
                    collection_id: collectionId,
                    stream_url: `${BUNNY_BASE_URL}/${videoId}/playlist.m3u8`,
                    thumbnail: decodeURIComponent(filePath),
                    document: documentId
                };
                const created = await strapi.entityService.create('api::upload-video.upload-video', {
                    data: data
                });

                resolve(ctx.send({
                    message: 'Tạo video thành công',
                    success: true
                }));

            }catch (err) {
                strapi.log.error('Lỗi khi upload video:', err);
                reject(ctx.send({
                    message: 'Lỗi khi upload video',
                    success: false
                }));
            }
        });
    },
    async handleWebhook(ctx) {
        try {
            const objMap = {
                "0" : "Queued",
                "1" : "Processing",
                "2" : "Encoding",
                "3" : "Finished",
                "4" : "Resolution",
            }
            const eventData = ctx.request.body;
            if (eventData) {
                const videos = await strapi.entityService.findMany('api::upload-video.upload-video', {
                    filters: { video_id: eventData.VideoGuid }
                });
                const video = videos[0];
                if (video) {
                    const updated = await strapi.entityService.update('api::upload-video.upload-video', video.id, {
                        data: {
                            status_video: objMap[eventData.Status.toString()],
                        }
                    });
                }
            }

        } catch (err) {
            console.error('Error handling webhook:', err);
            ctx.send({message: 'Failed to process webhook'});
        }
    }

}));




